# Modular City Map Generator

A web-based procedural city map generator with a modular, component-based architecture. Generate realistic city layouts with customizable parameters including road networks, districts, buildings, natural features, and infrastructure.

## Features

### Core Generation Components
- **Road Network Generator**: Creates grid, organic, or hybrid road patterns
- **District System**: Generates residential, commercial, and industrial zones
- **Building Generator**: Places contextual buildings based on district types
- **Natural Features**: Adds parks, water bodies, and green spaces
- **Infrastructure**: Places public buildings, utilities, and transport hubs

### Interactive Features
- **Real-time Parameter Control**: Adjust generation settings through intuitive UI
- **Reproducible Maps**: Use seeds for consistent map generation
- **Multiple Export Formats**: Save as PNG images or JSON data
- **Responsive Design**: Works on desktop and mobile devices

## Quick Start

1. **Open the Application**
   ```bash
   # Simply open index.html in a modern web browser
   # No build process or dependencies required
   ```

2. **Generate Your First Map**
   - Click "Generate Map" to create a city with default settings
   - Experiment with different parameters in the control panel
   - Use the seed field to recreate specific maps

3. **Customize Parameters**
   - **Map Size**: Choose from Small (800x600), Medium (1200x800), or Large (1600x1200)
   - **Road Pattern**: Select Grid, Organic, or Hybrid layouts
   - **Density Controls**: Adjust road and building density (0.1 to 1.0)
   - **District Types**: Enable/disable Residential, Commercial, Industrial zones
   - **Natural Features**: Toggle parks, water bodies, and infrastructure

## Architecture

### Component-Based Design
```
MapGenerator (Core Orchestrator)
├── RoadNetworkGenerator
├── DistrictGenerator  
├── BuildingGenerator
├── NaturalFeatureGenerator
├── InfrastructureGenerator
└── MapRenderer
```

### Key Classes

#### MapGenerator
The main orchestrator that coordinates all generation components:
- Manages configuration and state
- Coordinates generation phases
- Handles rendering and export

#### RoadNetworkGenerator
Creates road networks with different patterns:
- **Grid**: Regular street grid with variations
- **Organic**: Curved, natural-looking roads
- **Hybrid**: Combination of grid and organic elements

#### DistrictGenerator
Divides the city into functional zones:
- Uses Voronoi-like algorithm for realistic district boundaries
- Considers road intersections and random seeds
- Creates building blocks within districts

#### BuildingGenerator
Places buildings based on district context:
- Different building types per district (houses, offices, warehouses)
- Respects lot boundaries and density settings
- Adds architectural variety and details

### Generation Process

1. **Road Network**: Creates the transportation infrastructure
2. **Districts**: Divides city into functional zones
3. **Natural Features**: Places parks and water bodies
4. **Buildings**: Populates districts with appropriate structures
5. **Infrastructure**: Adds public buildings and utilities
6. **Rendering**: Draws all elements in proper layers

## Configuration Options

### Basic Settings
```javascript
{
  seed: "custom-seed",           // String or null for random
  mapSize: { width: 1200, height: 800 },
  roadPattern: "hybrid",         // "grid", "organic", "hybrid"
  roadDensity: 0.6,             // 0.1 to 1.0
  buildingDensity: 0.7          // 0.1 to 1.0
}
```

### District Configuration
```javascript
districts: {
  residential: true,    // Enable residential zones
  commercial: true,     // Enable commercial zones  
  industrial: true      // Enable industrial zones
}
```

### Feature Configuration
```javascript
features: {
  parks: true,          // Generate parks and green spaces
  water: true,          // Generate water bodies
  infrastructure: true  // Generate public buildings
}
```

## Examples

### Example 1: Dense Urban City
```javascript
{
  mapSize: { width: 1600, height: 1200 },
  roadPattern: "grid",
  roadDensity: 0.8,
  buildingDensity: 0.9,
  districts: { residential: true, commercial: true, industrial: true },
  features: { parks: true, water: false, infrastructure: true }
}
```

### Example 2: Organic Suburban Layout
```javascript
{
  mapSize: { width: 1200, height: 800 },
  roadPattern: "organic", 
  roadDensity: 0.4,
  buildingDensity: 0.5,
  districts: { residential: true, commercial: false, industrial: false },
  features: { parks: true, water: true, infrastructure: false }
}
```

### Example 3: Mixed Development
```javascript
{
  mapSize: { width: 1200, height: 800 },
  roadPattern: "hybrid",
  roadDensity: 0.6,
  buildingDensity: 0.7,
  districts: { residential: true, commercial: true, industrial: true },
  features: { parks: true, water: true, infrastructure: true }
}
```

## Export Formats

### Image Export (PNG)
- High-quality raster image of the generated map
- Suitable for presentations, printing, or sharing
- Maintains all visual details and colors

### Data Export (JSON)
Complete map data including:
```javascript
{
  config: { /* generation parameters */ },
  mapData: {
    roads: [ /* road network data */ ],
    districts: [ /* district polygons and properties */ ],
    buildings: [ /* building positions and types */ ],
    naturalFeatures: [ /* parks and water bodies */ ],
    infrastructure: [ /* public buildings and utilities */ ]
  },
  timestamp: "2024-01-01T00:00:00.000Z"
}
```

## Browser Compatibility

- **Chrome/Edge**: Full support
- **Firefox**: Full support  
- **Safari**: Full support
- **Mobile Browsers**: Responsive design with touch support

## Technical Requirements

- Modern web browser with HTML5 Canvas support
- JavaScript ES6+ support
- No external dependencies or build process required

## File Structure

```
MapGen/
├── index.html              # Main application page
├── styles/
│   └── main.css           # Application styles
├── js/
│   ├── main.js            # Application entry point
│   ├── core/
│   │   └── MapGenerator.js # Main generator class
│   ├── components/        # Generation components
│   │   ├── RoadNetworkGenerator.js
│   │   ├── DistrictGenerator.js
│   │   ├── BuildingGenerator.js
│   │   ├── NaturalFeatureGenerator.js
│   │   └── InfrastructureGenerator.js
│   ├── rendering/
│   │   └── MapRenderer.js # Canvas rendering
│   └── utils/
│       └── SeededRandom.js # Deterministic random numbers
└── README.md              # This documentation
```

## Contributing

The modular architecture makes it easy to extend:

1. **Add New Building Types**: Extend building configurations in `BuildingGenerator.js`
2. **Create New Road Patterns**: Add patterns to `RoadNetworkGenerator.js`
3. **Add Natural Features**: Extend feature types in `NaturalFeatureGenerator.js`
4. **Improve Rendering**: Enhance visual details in `MapRenderer.js`

## License

This project is open source and available under the MIT License.
