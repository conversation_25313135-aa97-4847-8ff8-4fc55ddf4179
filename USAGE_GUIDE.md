# City Map Generator - Usage Guide

## Getting Started

### Running the Application

1. **Local Development Server** (Recommended)
   ```bash
   # Navigate to the project directory
   cd MapGen
   
   # Start a local HTTP server (Python 3)
   python3 -m http.server 8000
   
   # Or using Python 2
   python -m SimpleHTTPServer 8000
   
   # Or using Node.js (if installed)
   npx http-server -p 8000
   ```

2. **Open in Browser**
   - Navigate to `http://localhost:8000`
   - The application will load with default settings

### First Map Generation

1. Click the **"Generate Map"** button to create your first city
2. The map will appear on the canvas with default parameters
3. Experiment with different settings in the control panel
4. Click **"Generate Map"** again to see the changes

## Control Panel Guide

### Generation Settings

#### Random Seed
- **Empty**: Generates a random seed each time
- **Custom Value**: Use any text to create reproducible maps
- **Example Seeds**: Try "downtown-2024", "suburb-test", or "coastal-city"

#### Map Size
- **Small (800x600)**: Good for quick testing and mobile viewing
- **Medium (1200x800)**: Default size, balanced for most uses
- **Large (1600x1200)**: High detail, best for export and printing

### Road Network Configuration

#### Road Pattern
- **Grid**: Traditional city blocks with perpendicular streets
  - Best for: Urban centers, downtown areas
  - Characteristics: Efficient traffic flow, easy navigation
  
- **Organic**: Curved, natural-looking roads
  - Best for: Suburban areas, hillside communities
  - Characteristics: More realistic, follows natural contours
  
- **Hybrid**: Combination of grid and organic elements
  - Best for: Mixed development, realistic cities
  - Characteristics: Grid backbone with organic secondary roads

#### Road Density (0.1 - 1.0)
- **Low (0.1-0.3)**: Sparse road network, rural feel
- **Medium (0.4-0.7)**: Balanced development
- **High (0.8-1.0)**: Dense urban network

### District Configuration

#### District Types
- **Residential**: Housing areas with homes and apartments
  - Buildings: Houses, apartment complexes
  - Characteristics: Lower density, more green space
  
- **Commercial**: Business and shopping areas
  - Buildings: Shops, offices, malls
  - Characteristics: Higher density, mixed building heights
  
- **Industrial**: Manufacturing and warehouse districts
  - Buildings: Factories, warehouses
  - Characteristics: Large buildings, utilitarian design

### Feature Settings

#### Building Density (0.1 - 1.0)
- **Low (0.1-0.3)**: Sparse development, lots of empty space
- **Medium (0.4-0.7)**: Realistic urban density
- **High (0.8-1.0)**: Dense urban development

#### Natural Features
- **Parks & Green Spaces**: Adds recreational areas and green corridors
- **Water Bodies**: Creates lakes, rivers, and water features
- **Infrastructure**: Places public buildings, utilities, and transport hubs

## Advanced Usage

### Creating Specific City Types

#### Dense Urban City
```
Settings:
- Map Size: Large
- Road Pattern: Grid
- Road Density: 0.8
- Building Density: 0.9
- All Districts: Enabled
- All Features: Enabled
```

#### Suburban Community
```
Settings:
- Map Size: Medium
- Road Pattern: Organic
- Road Density: 0.4
- Building Density: 0.5
- Districts: Residential only
- Features: Parks and Water enabled
```

#### Industrial Zone
```
Settings:
- Map Size: Medium
- Road Pattern: Grid
- Road Density: 0.6
- Building Density: 0.7
- Districts: Industrial only
- Features: Infrastructure only
```

### Using Seeds for Consistency

Seeds allow you to recreate the same map layout:

1. **Generate a map** you like
2. **Note the seed** displayed in the status message
3. **Enter the seed** in the seed field
4. **Generate again** to recreate the exact same layout
5. **Modify parameters** while keeping the same base layout

### Export Options

#### Image Export (PNG)
- **Use Case**: Presentations, printing, sharing
- **Quality**: High-resolution raster image
- **File Size**: Varies by map complexity (typically 100KB-2MB)

#### Data Export (JSON)
- **Use Case**: Data analysis, custom processing, backup
- **Contents**: Complete map data structure
- **File Size**: Typically 10-100KB depending on complexity

## Tips and Best Practices

### Performance Optimization

1. **Start Small**: Begin with small map sizes for experimentation
2. **Gradual Complexity**: Increase density and features gradually
3. **Browser Performance**: Large maps may take longer to generate

### Creating Realistic Cities

1. **Mixed Development**: Enable all district types for realistic variety
2. **Balanced Density**: Use medium density settings (0.5-0.7)
3. **Natural Features**: Include parks and water for realism
4. **Infrastructure**: Always include infrastructure for complete cities

### Troubleshooting

#### Map Not Generating
- Check browser console for JavaScript errors
- Ensure all district types aren't disabled
- Try refreshing the page

#### Poor Performance
- Reduce map size
- Lower density settings
- Close other browser tabs

#### Unexpected Results
- Try different seeds
- Adjust density parameters
- Check that desired features are enabled

## Keyboard Shortcuts

- **Ctrl/Cmd + S**: Quick save (triggers image export)
- **Space**: Generate new map (when canvas is focused)
- **R**: Reset to default settings

## Browser Compatibility

### Fully Supported
- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

### Mobile Support
- iOS Safari 13+
- Chrome Mobile 80+
- Samsung Internet 12+

## Common Use Cases

### Urban Planning
- Visualize different development scenarios
- Compare grid vs. organic layouts
- Test density impacts

### Game Development
- Generate city layouts for games
- Create varied urban environments
- Export data for game engines

### Education
- Demonstrate urban planning concepts
- Show impact of different city designs
- Interactive geography lessons

### Art and Design
- Create backgrounds for artwork
- Generate inspiration for illustrations
- Produce unique city silhouettes

## Next Steps

After mastering the basic controls:

1. **Experiment with Seeds**: Find interesting patterns
2. **Try Extreme Settings**: Push density to limits
3. **Export and Analyze**: Study the generated data
4. **Combine Exports**: Use multiple maps for larger regions
5. **Customize Code**: Modify the source for specific needs

## Support and Community

- **Issues**: Report bugs via GitHub issues
- **Features**: Suggest improvements
- **Contributions**: Submit pull requests
- **Examples**: Share interesting configurations
