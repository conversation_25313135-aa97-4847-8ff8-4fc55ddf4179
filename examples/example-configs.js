/**
 * Example configurations for the City Map Generator
 * These can be used as presets or starting points for custom maps
 */

export const ExampleConfigs = {
    
    /**
     * Dense Metropolitan City
     * High-density urban environment with grid roads and mixed development
     */
    denseMetropolitan: {
        name: "Dense Metropolitan",
        description: "High-density urban city with grid layout and mixed development",
        config: {
            seed: "metro-2024",
            mapSize: { width: 1600, height: 1200 },
            roadPattern: "grid",
            roadDensity: 0.8,
            buildingDensity: 0.9,
            districts: {
                residential: true,
                commercial: true,
                industrial: true
            },
            features: {
                parks: true,
                water: false,
                infrastructure: true
            }
        }
    },

    /**
     * Suburban Sprawl
     * Low-density residential area with organic road patterns
     */
    suburbanSprawl: {
        name: "Suburban Sprawl",
        description: "Low-density residential area with curved roads and lots of green space",
        config: {
            seed: "suburb-peaceful",
            mapSize: { width: 1200, height: 800 },
            roadPattern: "organic",
            roadDensity: 0.4,
            buildingDensity: 0.5,
            districts: {
                residential: true,
                commercial: false,
                industrial: false
            },
            features: {
                parks: true,
                water: true,
                infrastructure: false
            }
        }
    },

    /**
     * Industrial District
     * Heavy industrial area with efficient grid layout
     */
    industrialDistrict: {
        name: "Industrial District",
        description: "Manufacturing and warehouse district with efficient transportation",
        config: {
            seed: "industry-hub",
            mapSize: { width: 1200, height: 800 },
            roadPattern: "grid",
            roadDensity: 0.6,
            buildingDensity: 0.7,
            districts: {
                residential: false,
                commercial: false,
                industrial: true
            },
            features: {
                parks: false,
                water: false,
                infrastructure: true
            }
        }
    },

    /**
     * Coastal Town
     * Mixed development with water features and organic layout
     */
    coastalTown: {
        name: "Coastal Town",
        description: "Seaside community with water features and mixed development",
        config: {
            seed: "coastal-breeze",
            mapSize: { width: 1200, height: 800 },
            roadPattern: "hybrid",
            roadDensity: 0.5,
            buildingDensity: 0.6,
            districts: {
                residential: true,
                commercial: true,
                industrial: false
            },
            features: {
                parks: true,
                water: true,
                infrastructure: true
            }
        }
    },

    /**
     * Historic Downtown
     * Dense commercial core with mixed-use development
     */
    historicDowntown: {
        name: "Historic Downtown",
        description: "Dense commercial core with traditional grid layout",
        config: {
            seed: "historic-center",
            mapSize: { width: 800, height: 600 },
            roadPattern: "grid",
            roadDensity: 0.7,
            buildingDensity: 0.8,
            districts: {
                residential: false,
                commercial: true,
                industrial: false
            },
            features: {
                parks: true,
                water: false,
                infrastructure: true
            }
        }
    },

    /**
     * Garden City
     * Balanced development with emphasis on green spaces
     */
    gardenCity: {
        name: "Garden City",
        description: "Balanced urban development with abundant parks and green corridors",
        config: {
            seed: "garden-paradise",
            mapSize: { width: 1200, height: 800 },
            roadPattern: "hybrid",
            roadDensity: 0.6,
            buildingDensity: 0.6,
            districts: {
                residential: true,
                commercial: true,
                industrial: false
            },
            features: {
                parks: true,
                water: true,
                infrastructure: true
            }
        }
    },

    /**
     * Tech Campus
     * Modern development with organic roads and mixed-use buildings
     */
    techCampus: {
        name: "Tech Campus",
        description: "Modern technology district with innovative layout",
        config: {
            seed: "tech-innovation",
            mapSize: { width: 1200, height: 800 },
            roadPattern: "organic",
            roadDensity: 0.5,
            buildingDensity: 0.7,
            districts: {
                residential: false,
                commercial: true,
                industrial: false
            },
            features: {
                parks: true,
                water: true,
                infrastructure: true
            }
        }
    },

    /**
     * Rural Township
     * Small town with minimal development and natural features
     */
    ruralTownship: {
        name: "Rural Township",
        description: "Small rural community with minimal development",
        config: {
            seed: "rural-quiet",
            mapSize: { width: 800, height: 600 },
            roadPattern: "organic",
            roadDensity: 0.3,
            buildingDensity: 0.3,
            districts: {
                residential: true,
                commercial: false,
                industrial: false
            },
            features: {
                parks: true,
                water: true,
                infrastructure: false
            }
        }
    },

    /**
     * Mixed Use Development
     * Balanced city with all district types and features
     */
    mixedUseDevelopment: {
        name: "Mixed Use Development",
        description: "Balanced city with all district types and comprehensive features",
        config: {
            seed: "mixed-balance",
            mapSize: { width: 1200, height: 800 },
            roadPattern: "hybrid",
            roadDensity: 0.6,
            buildingDensity: 0.7,
            districts: {
                residential: true,
                commercial: true,
                industrial: true
            },
            features: {
                parks: true,
                water: true,
                infrastructure: true
            }
        }
    },

    /**
     * Experimental Layout
     * Unique configuration for testing edge cases
     */
    experimentalLayout: {
        name: "Experimental Layout",
        description: "Unique configuration for testing different generation parameters",
        config: {
            seed: "experiment-alpha",
            mapSize: { width: 1600, height: 1200 },
            roadPattern: "organic",
            roadDensity: 0.9,
            buildingDensity: 0.4,
            districts: {
                residential: true,
                commercial: true,
                industrial: true
            },
            features: {
                parks: true,
                water: true,
                infrastructure: true
            }
        }
    }
};

/**
 * Apply an example configuration to the map generator
 */
export function applyExampleConfig(mapGenerator, configName) {
    const example = ExampleConfigs[configName];
    if (!example) {
        console.error(`Example configuration '${configName}' not found`);
        return false;
    }
    
    mapGenerator.updateConfig(example.config);
    console.log(`Applied example configuration: ${example.name}`);
    return true;
}

/**
 * Get list of available example configurations
 */
export function getAvailableExamples() {
    return Object.keys(ExampleConfigs).map(key => ({
        key: key,
        name: ExampleConfigs[key].name,
        description: ExampleConfigs[key].description
    }));
}

/**
 * Generate random configuration for experimentation
 */
export function generateRandomConfig(random) {
    const mapSizes = [
        { width: 800, height: 600 },
        { width: 1200, height: 800 },
        { width: 1600, height: 1200 }
    ];
    
    const roadPatterns = ['grid', 'organic', 'hybrid'];
    
    return {
        seed: `random-${Date.now()}`,
        mapSize: random.choice(mapSizes),
        roadPattern: random.choice(roadPatterns),
        roadDensity: random.nextFloat(0.3, 0.9),
        buildingDensity: random.nextFloat(0.4, 0.8),
        districts: {
            residential: random.nextBoolean(0.8),
            commercial: random.nextBoolean(0.7),
            industrial: random.nextBoolean(0.5)
        },
        features: {
            parks: random.nextBoolean(0.8),
            water: random.nextBoolean(0.6),
            infrastructure: random.nextBoolean(0.7)
        }
    };
}
