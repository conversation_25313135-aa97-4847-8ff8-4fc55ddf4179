/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f5f5;
    color: #333;
    line-height: 1.6;
}

/* App layout */
.app-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.app-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem 2rem;
    text-align: center;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.app-header h1 {
    font-size: 2.5rem;
    margin-bottom: 0.5rem;
    font-weight: 300;
}

.app-header p {
    opacity: 0.9;
    font-size: 1.1rem;
}

.main-content {
    flex: 1;
    display: grid;
    grid-template-columns: 300px 1fr;
    gap: 1rem;
    padding: 1rem;
    max-width: 1800px;
    margin: 0 auto;
    width: 100%;
}

/* Control panel */
.control-panel {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    padding: 1.5rem;
    height: fit-content;
    position: sticky;
    top: 1rem;
}

.panel-section {
    margin-bottom: 2rem;
}

.panel-section:last-child {
    margin-bottom: 0;
}

.panel-section h3 {
    color: #667eea;
    margin-bottom: 1rem;
    font-size: 1.1rem;
    font-weight: 600;
    border-bottom: 2px solid #f0f0f0;
    padding-bottom: 0.5rem;
}

.control-group {
    margin-bottom: 1rem;
}

.control-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #555;
}

.control-group input[type="text"],
.control-group select {
    width: 100%;
    padding: 0.5rem;
    border: 2px solid #e0e0e0;
    border-radius: 4px;
    font-size: 0.9rem;
    transition: border-color 0.3s ease;
}

.control-group input[type="text"]:focus,
.control-group select:focus {
    outline: none;
    border-color: #667eea;
}

.control-group input[type="range"] {
    width: calc(100% - 50px);
    margin-right: 10px;
}

.range-value {
    font-weight: 600;
    color: #667eea;
    min-width: 40px;
    display: inline-block;
}

.control-group input[type="checkbox"] {
    margin-right: 0.5rem;
}

.control-group label:has(input[type="checkbox"]) {
    display: flex;
    align-items: center;
    cursor: pointer;
    margin-bottom: 0.5rem;
}

/* Buttons */
.button-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.primary-btn, .secondary-btn {
    padding: 0.75rem 1rem;
    border: none;
    border-radius: 4px;
    font-size: 0.9rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.primary-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.primary-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.secondary-btn {
    background: #f8f9fa;
    color: #667eea;
    border: 2px solid #667eea;
}

.secondary-btn:hover {
    background: #667eea;
    color: white;
}

/* Map container */
.map-container {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    padding: 1rem;
    display: flex;
    flex-direction: column;
    align-items: center;
}

#mapCanvas {
    border: 2px solid #e0e0e0;
    border-radius: 4px;
    background: #fafafa;
    cursor: crosshair;
}

.map-info {
    margin-top: 1rem;
    padding: 0.5rem 1rem;
    background: #f8f9fa;
    border-radius: 4px;
    font-size: 0.9rem;
    color: #666;
}

/* Responsive design */
@media (max-width: 1200px) {
    .main-content {
        grid-template-columns: 280px 1fr;
    }
    
    #mapCanvas {
        max-width: 100%;
        height: auto;
    }
}

@media (max-width: 768px) {
    .main-content {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }
    
    .control-panel {
        position: static;
        order: 2;
    }
    
    .app-header h1 {
        font-size: 2rem;
    }
    
    .app-header p {
        font-size: 1rem;
    }
}
