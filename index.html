<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modular City Map Generator</title>
    <link rel="stylesheet" href="styles/main.css">
</head>
<body>
    <div class="app-container">
        <header class="app-header">
            <h1>City Map Generator</h1>
            <p>Generate procedural city maps with customizable parameters</p>
        </header>
        
        <div class="main-content">
            <aside class="control-panel">
                <div class="panel-section">
                    <h3>Generation Settings</h3>
                    <div class="control-group">
                        <label for="seed">Random Seed:</label>
                        <input type="text" id="seed" placeholder="Enter seed or leave empty">
                    </div>
                    
                    <div class="control-group">
                        <label for="mapSize">Map Size:</label>
                        <select id="mapSize">
                            <option value="small">Small (800x600)</option>
                            <option value="medium" selected>Medium (1200x800)</option>
                            <option value="large">Large (1600x1200)</option>
                        </select>
                    </div>
                </div>

                <div class="panel-section">
                    <h3>Road Network</h3>
                    <div class="control-group">
                        <label for="roadPattern">Pattern:</label>
                        <select id="roadPattern">
                            <option value="grid">Grid</option>
                            <option value="organic">Organic</option>
                            <option value="hybrid" selected>Hybrid</option>
                        </select>
                    </div>
                    
                    <div class="control-group">
                        <label for="roadDensity">Density:</label>
                        <input type="range" id="roadDensity" min="0.1" max="1.0" step="0.1" value="0.6">
                        <span class="range-value">0.6</span>
                    </div>
                </div>

                <div class="panel-section">
                    <h3>Districts</h3>
                    <div class="control-group">
                        <label>
                            <input type="checkbox" id="residential" checked>
                            Residential
                        </label>
                    </div>
                    <div class="control-group">
                        <label>
                            <input type="checkbox" id="commercial" checked>
                            Commercial
                        </label>
                    </div>
                    <div class="control-group">
                        <label>
                            <input type="checkbox" id="industrial" checked>
                            Industrial
                        </label>
                    </div>
                </div>

                <div class="panel-section">
                    <h3>Features</h3>
                    <div class="control-group">
                        <label for="buildingDensity">Building Density:</label>
                        <input type="range" id="buildingDensity" min="0.1" max="1.0" step="0.1" value="0.7">
                        <span class="range-value">0.7</span>
                    </div>
                    
                    <div class="control-group">
                        <label>
                            <input type="checkbox" id="parks" checked>
                            Parks & Green Spaces
                        </label>
                    </div>
                    
                    <div class="control-group">
                        <label>
                            <input type="checkbox" id="water" checked>
                            Water Bodies
                        </label>
                    </div>
                    
                    <div class="control-group">
                        <label>
                            <input type="checkbox" id="infrastructure" checked>
                            Infrastructure
                        </label>
                    </div>
                </div>

                <div class="panel-section">
                    <div class="button-group">
                        <button id="generateBtn" class="primary-btn">Generate Map</button>
                        <button id="exportImageBtn" class="secondary-btn">Export Image</button>
                        <button id="exportDataBtn" class="secondary-btn">Export Data</button>
                    </div>
                </div>
            </aside>

            <main class="map-container">
                <canvas id="mapCanvas" width="1200" height="800"></canvas>
                <div class="map-info">
                    <span id="mapStats">Ready to generate</span>
                </div>
            </main>
        </div>
    </div>

    <!-- Core modules -->
    <script type="module" src="js/core/MapGenerator.js"></script>
    <script type="module" src="js/main.js"></script>
</body>
</html>
