/**
 * Main application entry point
 */

import { MapGenerator } from './core/MapGenerator.js';

class CityMapApp {
    constructor() {
        this.mapGenerator = null;
        this.isGenerating = false;
        
        this.initializeApp();
    }

    /**
     * Initialize the application
     */
    initializeApp() {
        // Get canvas and initialize map generator
        const canvas = document.getElementById('mapCanvas');
        if (!canvas) {
            console.error('Canvas element not found');
            return;
        }
        
        this.mapGenerator = new MapGenerator(canvas);
        
        // Set up event listeners
        this.setupEventListeners();
        
        // Initialize UI
        this.updateUI();
        
        console.log('City Map Generator initialized');
    }

    /**
     * Set up all event listeners
     */
    setupEventListeners() {
        // Generation button
        const generateBtn = document.getElementById('generateBtn');
        if (generateBtn) {
            generateBtn.addEventListener('click', () => this.generateMap());
        }
        
        // Export buttons
        const exportImageBtn = document.getElementById('exportImageBtn');
        if (exportImageBtn) {
            exportImageBtn.addEventListener('click', () => this.exportImage());
        }
        
        const exportDataBtn = document.getElementById('exportDataBtn');
        if (exportDataBtn) {
            exportDataBtn.addEventListener('click', () => this.exportData());
        }
        
        // Map size selector
        const mapSizeSelect = document.getElementById('mapSize');
        if (mapSizeSelect) {
            mapSizeSelect.addEventListener('change', (e) => this.updateMapSize(e.target.value));
        }
        
        // Range inputs with live updates
        this.setupRangeInputs();
        
        // Checkbox inputs
        this.setupCheckboxInputs();
    }

    /**
     * Set up range input listeners
     */
    setupRangeInputs() {
        const rangeInputs = ['roadDensity', 'buildingDensity'];
        
        rangeInputs.forEach(inputId => {
            const input = document.getElementById(inputId);
            const valueSpan = input?.nextElementSibling;
            
            if (input && valueSpan) {
                input.addEventListener('input', (e) => {
                    valueSpan.textContent = e.target.value;
                });
            }
        });
    }

    /**
     * Set up checkbox input listeners
     */
    setupCheckboxInputs() {
        const checkboxes = ['residential', 'commercial', 'industrial', 'parks', 'water', 'infrastructure'];
        
        checkboxes.forEach(checkboxId => {
            const checkbox = document.getElementById(checkboxId);
            if (checkbox) {
                checkbox.addEventListener('change', () => {
                    // Could add live preview updates here
                });
            }
        });
    }

    /**
     * Generate a new map
     */
    async generateMap() {
        if (this.isGenerating) {
            console.log('Generation already in progress');
            return;
        }
        
        this.isGenerating = true;
        this.updateGenerationStatus('Generating map...', true);
        
        try {
            // Get current configuration from UI
            const config = this.getConfigFromUI();
            
            // Update map generator configuration
            this.mapGenerator.updateConfig(config);
            
            // Generate the map
            const result = await this.mapGenerator.generateMap();
            
            if (result.success) {
                this.updateGenerationStatus(`Map generated successfully! Seed: ${result.seed}`, false);
                this.updateMapStats(result.stats);
            } else {
                this.updateGenerationStatus(`Generation failed: ${result.error}`, false);
            }
            
        } catch (error) {
            console.error('Map generation error:', error);
            this.updateGenerationStatus(`Error: ${error.message}`, false);
        } finally {
            this.isGenerating = false;
        }
    }

    /**
     * Get configuration from UI elements
     */
    getConfigFromUI() {
        const config = {};
        
        // Seed
        const seedInput = document.getElementById('seed');
        if (seedInput && seedInput.value.trim()) {
            config.seed = seedInput.value.trim();
        }
        
        // Map size
        const mapSizeSelect = document.getElementById('mapSize');
        if (mapSizeSelect) {
            config.mapSize = this.getMapSizeFromValue(mapSizeSelect.value);
        }
        
        // Road pattern
        const roadPatternSelect = document.getElementById('roadPattern');
        if (roadPatternSelect) {
            config.roadPattern = roadPatternSelect.value;
        }
        
        // Densities
        const roadDensityInput = document.getElementById('roadDensity');
        if (roadDensityInput) {
            config.roadDensity = parseFloat(roadDensityInput.value);
        }
        
        const buildingDensityInput = document.getElementById('buildingDensity');
        if (buildingDensityInput) {
            config.buildingDensity = parseFloat(buildingDensityInput.value);
        }
        
        // Districts
        config.districts = {
            residential: document.getElementById('residential')?.checked || false,
            commercial: document.getElementById('commercial')?.checked || false,
            industrial: document.getElementById('industrial')?.checked || false
        };
        
        // Features
        config.features = {
            parks: document.getElementById('parks')?.checked || false,
            water: document.getElementById('water')?.checked || false,
            infrastructure: document.getElementById('infrastructure')?.checked || false
        };
        
        return config;
    }

    /**
     * Get map size object from select value
     */
    getMapSizeFromValue(value) {
        const sizes = {
            small: { width: 800, height: 600 },
            medium: { width: 1200, height: 800 },
            large: { width: 1600, height: 1200 }
        };
        
        return sizes[value] || sizes.medium;
    }

    /**
     * Update map size
     */
    updateMapSize(sizeValue) {
        const size = this.getMapSizeFromValue(sizeValue);
        const canvas = document.getElementById('mapCanvas');
        
        if (canvas) {
            canvas.width = size.width;
            canvas.height = size.height;
            
            // Update map generator if it exists
            if (this.mapGenerator) {
                this.mapGenerator.updateConfig({ mapSize: size });
            }
        }
    }

    /**
     * Export map as image
     */
    exportImage() {
        if (this.mapGenerator) {
            this.mapGenerator.exportImage();
        } else {
            alert('Please generate a map first');
        }
    }

    /**
     * Export map data as JSON
     */
    exportData() {
        if (this.mapGenerator) {
            this.mapGenerator.exportData();
        } else {
            alert('Please generate a map first');
        }
    }

    /**
     * Update generation status
     */
    updateGenerationStatus(message, isGenerating) {
        const statusElement = document.getElementById('mapStats');
        const generateBtn = document.getElementById('generateBtn');
        
        if (statusElement) {
            statusElement.textContent = message;
        }
        
        if (generateBtn) {
            generateBtn.disabled = isGenerating;
            generateBtn.textContent = isGenerating ? 'Generating...' : 'Generate Map';
        }
    }

    /**
     * Update map statistics display
     */
    updateMapStats(stats) {
        const statsText = `Generated: ${stats.roads} roads, ${stats.districts} districts, ${stats.buildings} buildings, ${stats.naturalFeatures} natural features, ${stats.infrastructure} infrastructure`;
        
        const statusElement = document.getElementById('mapStats');
        if (statusElement) {
            statusElement.textContent = statsText;
        }
    }

    /**
     * Update UI elements
     */
    updateUI() {
        // Set initial values for range inputs
        const roadDensityInput = document.getElementById('roadDensity');
        const roadDensityValue = roadDensityInput?.nextElementSibling;
        if (roadDensityInput && roadDensityValue) {
            roadDensityValue.textContent = roadDensityInput.value;
        }
        
        const buildingDensityInput = document.getElementById('buildingDensity');
        const buildingDensityValue = buildingDensityInput?.nextElementSibling;
        if (buildingDensityInput && buildingDensityValue) {
            buildingDensityValue.textContent = buildingDensityInput.value;
        }
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new CityMapApp();
});
