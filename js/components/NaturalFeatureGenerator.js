/**
 * Natural Feature Generator - Creates parks, water bodies, and green spaces
 */

export class NaturalFeatureGenerator {
    constructor() {
        this.featureTypes = {
            PARK: {
                color: '#228B22',
                minSize: 30,
                maxSize: 100,
                shapes: ['circle', 'oval', 'irregular'],
                features: ['trees', 'paths', 'playground', 'fountain']
            },
            WATER: {
                color: '#4169E1',
                minSize: 40,
                maxSize: 150,
                shapes: ['circle', 'oval', 'river', 'irregular'],
                features: ['bridge', 'dock', 'fountain']
            },
            FOREST: {
                color: '#006400',
                minSize: 50,
                maxSize: 200,
                shapes: ['irregular', 'oval'],
                features: ['trees', 'trails']
            },
            PLAZA: {
                color: '#D2B48C',
                minSize: 20,
                maxSize: 60,
                shapes: ['square', 'circle'],
                features: ['fountain', 'benches', 'statue']
            }
        };
    }

    /**
     * Generate natural features based on configuration
     */
    async generate(config, random) {
        const { parks, water, roads, districts, bounds } = config;
        const features = [];
        
        if (parks) {
            const parkFeatures = await this._generateParks(districts, roads, bounds, random);
            features.push(...parkFeatures);
        }
        
        if (water) {
            const waterFeatures = await this._generateWaterBodies(districts, roads, bounds, random);
            features.push(...waterFeatures);
        }
        
        // Add some additional green spaces
        const additionalFeatures = await this._generateAdditionalFeatures(districts, roads, bounds, random);
        features.push(...additionalFeatures);
        
        return features;
    }

    /**
     * Generate parks and green spaces
     */
    async _generateParks(districts, roads, bounds, random) {
        const parks = [];
        const numParks = random.nextInt(3, 8);
        
        for (let i = 0; i < numParks; i++) {
            const park = this._createNaturalFeature('PARK', districts, roads, bounds, random);
            if (park && !this._overlapsExistingFeatures(park, parks)) {
                parks.push(park);
            }
        }
        
        return parks;
    }

    /**
     * Generate water bodies
     */
    async _generateWaterBodies(districts, roads, bounds, random) {
        const waterBodies = [];
        const numWaterBodies = random.nextInt(1, 4);
        
        for (let i = 0; i < numWaterBodies; i++) {
            const waterBody = this._createNaturalFeature('WATER', districts, roads, bounds, random);
            if (waterBody && !this._overlapsExistingFeatures(waterBody, waterBodies)) {
                waterBodies.push(waterBody);
            }
        }
        
        return waterBodies;
    }

    /**
     * Generate additional features like plazas and small forests
     */
    async _generateAdditionalFeatures(districts, roads, bounds, random) {
        const features = [];
        
        // Add plazas in commercial districts
        const commercialDistricts = districts.filter(d => d.type === 'COMMERCIAL');
        for (const district of commercialDistricts) {
            if (random.nextBoolean(0.6)) {
                const plaza = this._createDistrictFeature('PLAZA', district, random);
                if (plaza) {
                    features.push(plaza);
                }
            }
        }
        
        // Add small forests on outskirts
        if (random.nextBoolean(0.4)) {
            const forest = this._createNaturalFeature('FOREST', districts, roads, bounds, random);
            if (forest) {
                features.push(forest);
            }
        }
        
        return features;
    }

    /**
     * Create a natural feature
     */
    _createNaturalFeature(type, districts, roads, bounds, random) {
        const config = this.featureTypes[type];
        const size = random.nextInt(config.minSize, config.maxSize);
        
        // Find suitable location
        let attempts = 0;
        let position = null;
        
        while (attempts < 20 && !position) {
            const candidate = {
                x: random.nextFloat(size, bounds.width - size),
                y: random.nextFloat(size, bounds.height - size)
            };
            
            if (this._isValidPosition(candidate, size, districts, roads)) {
                position = candidate;
            }
            attempts++;
        }
        
        if (!position) return null;
        
        const shape = random.choice(config.shapes);
        const feature = {
            id: `${type.toLowerCase()}_${Date.now()}_${random.nextInt(1000, 9999)}`,
            type: type,
            x: position.x,
            y: position.y,
            size: size,
            shape: shape,
            color: config.color,
            features: this._selectFeatureElements(config.features, random),
            geometry: this._generateFeatureGeometry(shape, position, size, random)
        };
        
        return feature;
    }

    /**
     * Create a feature within a specific district
     */
    _createDistrictFeature(type, district, random) {
        const config = this.featureTypes[type];
        const size = random.nextInt(config.minSize, Math.min(config.maxSize, 40));
        
        // Find position within district
        const bounds = this._getPolygonBounds(district.polygon);
        const centerX = (bounds.minX + bounds.maxX) / 2;
        const centerY = (bounds.minY + bounds.maxY) / 2;
        
        const position = {
            x: centerX + random.nextFloat(-size, size),
            y: centerY + random.nextFloat(-size, size)
        };
        
        // Check if position is within district
        if (!this._pointInPolygon(position, district.polygon)) {
            return null;
        }
        
        const shape = random.choice(config.shapes);
        const feature = {
            id: `${type.toLowerCase()}_${district.id}_${random.nextInt(1000, 9999)}`,
            type: type,
            x: position.x,
            y: position.y,
            size: size,
            shape: shape,
            color: config.color,
            features: this._selectFeatureElements(config.features, random),
            geometry: this._generateFeatureGeometry(shape, position, size, random),
            districtId: district.id
        };
        
        return feature;
    }

    /**
     * Generate geometry for different feature shapes
     */
    _generateFeatureGeometry(shape, position, size, random) {
        switch (shape) {
            case 'circle':
                return {
                    type: 'circle',
                    center: position,
                    radius: size / 2
                };
                
            case 'oval':
                return {
                    type: 'ellipse',
                    center: position,
                    radiusX: size / 2,
                    radiusY: size / 2 * random.nextFloat(0.6, 1.4),
                    rotation: random.nextAngle()
                };
                
            case 'square':
                return {
                    type: 'rectangle',
                    x: position.x - size / 2,
                    y: position.y - size / 2,
                    width: size,
                    height: size
                };
                
            case 'river':
                return this._generateRiverGeometry(position, size, random);
                
            case 'irregular':
                return this._generateIrregularGeometry(position, size, random);
                
            default:
                return {
                    type: 'circle',
                    center: position,
                    radius: size / 2
                };
        }
    }

    /**
     * Generate river-like geometry
     */
    _generateRiverGeometry(position, size, random) {
        const points = [];
        const numPoints = random.nextInt(4, 8);
        const width = size / 4;
        
        // Generate meandering path
        let currentX = position.x - size;
        let currentY = position.y + random.nextFloat(-size / 4, size / 4);
        
        for (let i = 0; i <= numPoints; i++) {
            points.push({ x: currentX, y: currentY });
            
            currentX += size * 2 / numPoints;
            currentY += random.nextFloat(-size / 3, size / 3);
        }
        
        return {
            type: 'path',
            points: points,
            width: width
        };
    }

    /**
     * Generate irregular polygon geometry
     */
    _generateIrregularGeometry(position, size, random) {
        const points = [];
        const numPoints = random.nextInt(6, 12);
        const baseRadius = size / 2;
        
        for (let i = 0; i < numPoints; i++) {
            const angle = (i / numPoints) * Math.PI * 2;
            const radius = baseRadius * random.nextFloat(0.7, 1.3);
            
            points.push({
                x: position.x + Math.cos(angle) * radius,
                y: position.y + Math.sin(angle) * radius
            });
        }
        
        return {
            type: 'polygon',
            points: points
        };
    }

    /**
     * Select feature elements (trees, paths, etc.)
     */
    _selectFeatureElements(availableFeatures, random) {
        const selected = [];
        const numFeatures = random.nextInt(1, Math.min(3, availableFeatures.length));
        
        const shuffled = random.shuffle([...availableFeatures]);
        for (let i = 0; i < numFeatures; i++) {
            selected.push(shuffled[i]);
        }
        
        return selected;
    }

    /**
     * Check if position is valid for placing a natural feature
     */
    _isValidPosition(position, size, districts, roads) {
        // Check distance from major roads
        for (const road of roads) {
            if (road.type === 'HIGHWAY' || road.type === 'MAJOR') {
                const distance = this._distanceToRoad(position, road);
                if (distance < size / 2 + 10) {
                    return false;
                }
            }
        }
        
        // Prefer areas not in industrial districts
        for (const district of districts) {
            if (district.type === 'INDUSTRIAL' && 
                this._pointInPolygon(position, district.polygon)) {
                return random.nextBoolean(0.2); // Low probability in industrial areas
            }
        }
        
        return true;
    }

    /**
     * Check if feature overlaps with existing features
     */
    _overlapsExistingFeatures(newFeature, existingFeatures) {
        for (const existing of existingFeatures) {
            const distance = this._distance(
                { x: newFeature.x, y: newFeature.y },
                { x: existing.x, y: existing.y }
            );
            
            if (distance < (newFeature.size + existing.size) / 2) {
                return true;
            }
        }
        return false;
    }

    /**
     * Utility functions
     */
    _distance(p1, p2) {
        return Math.sqrt(Math.pow(p2.x - p1.x, 2) + Math.pow(p2.y - p1.y, 2));
    }

    _distanceToRoad(point, road) {
        // Simplified distance calculation to first road point
        if (road.points && road.points.length > 0) {
            return this._distance(point, road.points[0]);
        }
        return Infinity;
    }

    _pointInPolygon(point, polygon) {
        let inside = false;
        for (let i = 0, j = polygon.length - 1; i < polygon.length; j = i++) {
            if (((polygon[i].y > point.y) !== (polygon[j].y > point.y)) &&
                (point.x < (polygon[j].x - polygon[i].x) * (point.y - polygon[i].y) / (polygon[j].y - polygon[i].y) + polygon[i].x)) {
                inside = !inside;
            }
        }
        return inside;
    }

    _getPolygonBounds(polygon) {
        let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity;
        
        polygon.forEach(point => {
            minX = Math.min(minX, point.x);
            minY = Math.min(minY, point.y);
            maxX = Math.max(maxX, point.x);
            maxY = Math.max(maxY, point.y);
        });
        
        return { minX, minY, maxX, maxY };
    }
}
