/**
 * Building Generator - Places and sizes buildings based on districts and density
 */

export class BuildingGenerator {
    constructor() {
        this.buildingTypes = {
            RESIDENTIAL: {
                HOUSE: { 
                    width: { min: 8, max: 15 }, 
                    height: { min: 8, max: 12 }, 
                    floors: { min: 1, max: 2 },
                    color: '#8FBC8F',
                    probability: 0.7
                },
                APARTMENT: { 
                    width: { min: 20, max: 40 }, 
                    height: { min: 15, max: 25 }, 
                    floors: { min: 3, max: 6 },
                    color: '#9ACD32',
                    probability: 0.3
                }
            },
            COMMERCIAL: {
                SHOP: { 
                    width: { min: 10, max: 20 }, 
                    height: { min: 10, max: 15 }, 
                    floors: { min: 1, max: 3 },
                    color: '#FFB6C1',
                    probability: 0.5
                },
                OFFICE: { 
                    width: { min: 25, max: 50 }, 
                    height: { min: 20, max: 40 }, 
                    floors: { min: 4, max: 12 },
                    color: '#DDA0DD',
                    probability: 0.3
                },
                MALL: { 
                    width: { min: 60, max: 100 }, 
                    height: { min: 40, max: 60 }, 
                    floors: { min: 2, max: 4 },
                    color: '#F0E68C',
                    probability: 0.2
                }
            },
            INDUSTRIAL: {
                WAREHOUSE: { 
                    width: { min: 30, max: 80 }, 
                    height: { min: 20, max: 40 }, 
                    floors: { min: 1, max: 2 },
                    color: '#CD853F',
                    probability: 0.6
                },
                FACTORY: { 
                    width: { min: 40, max: 100 }, 
                    height: { min: 25, max: 50 }, 
                    floors: { min: 1, max: 3 },
                    color: '#A0522D',
                    probability: 0.4
                }
            }
        };
    }

    /**
     * Generate buildings based on districts and configuration
     */
    async generate(config, random) {
        const { density, roads, districts, naturalFeatures, bounds } = config;
        const buildings = [];
        
        // Generate buildings for each district
        for (const district of districts) {
            const districtBuildings = await this._generateDistrictBuildings(
                district, density, roads, naturalFeatures, random
            );
            buildings.push(...districtBuildings);
        }
        
        return buildings;
    }

    /**
     * Generate buildings for a specific district
     */
    async _generateDistrictBuildings(district, density, roads, naturalFeatures, random) {
        const buildings = [];
        const districtDensity = density * district.properties.density;
        
        // Process each block in the district
        for (const block of district.blocks) {
            const blockBuildings = this._generateBlockBuildings(
                block, district, districtDensity, random
            );
            buildings.push(...blockBuildings);
        }
        
        return buildings;
    }

    /**
     * Generate buildings for a city block
     */
    _generateBlockBuildings(block, district, density, random) {
        const buildings = [];
        const availableLots = block.buildingLots.filter(lot => !lot.occupied);
        
        // Determine how many lots to fill based on density
        const lotsToFill = Math.floor(availableLots.length * density);
        const selectedLots = random.shuffle([...availableLots]).slice(0, lotsToFill);
        
        for (const lot of selectedLots) {
            const building = this._createBuilding(lot, district, random);
            if (building) {
                buildings.push(building);
                lot.occupied = true;
            }
        }
        
        return buildings;
    }

    /**
     * Create a single building on a lot
     */
    _createBuilding(lot, district, random) {
        const buildingType = this._selectBuildingType(district.type, random);
        if (!buildingType) return null;
        
        const typeConfig = this.buildingTypes[district.type][buildingType];
        
        // Calculate building dimensions
        const width = random.nextInt(typeConfig.width.min, typeConfig.width.max);
        const height = random.nextInt(typeConfig.height.min, typeConfig.height.max);
        const floors = random.nextInt(typeConfig.floors.min, typeConfig.floors.max);
        
        // Ensure building fits in lot
        const actualWidth = Math.min(width, lot.width - 2);
        const actualHeight = Math.min(height, lot.height - 2);
        
        if (actualWidth < 4 || actualHeight < 4) {
            return null; // Too small to place building
        }
        
        // Position building within lot (with small margin)
        const x = lot.x + 1 + random.nextFloat(0, lot.width - actualWidth - 2);
        const y = lot.y + 1 + random.nextFloat(0, lot.height - actualHeight - 2);
        
        const building = {
            id: `building_${Date.now()}_${random.nextInt(1000, 9999)}`,
            type: buildingType,
            districtType: district.type,
            x: x,
            y: y,
            width: actualWidth,
            height: actualHeight,
            floors: floors,
            color: this._getBuildingColor(typeConfig.color, random),
            rotation: random.nextFloat(-5, 5), // Slight rotation for variety
            features: this._generateBuildingFeatures(buildingType, district.type, random)
        };
        
        return building;
    }

    /**
     * Select building type based on district and probabilities
     */
    _selectBuildingType(districtType, random) {
        const types = this.buildingTypes[districtType];
        if (!types) return null;
        
        const roll = random.next();
        let cumulative = 0;
        
        for (const [typeName, config] of Object.entries(types)) {
            cumulative += config.probability;
            if (roll <= cumulative) {
                return typeName;
            }
        }
        
        // Fallback to first type
        return Object.keys(types)[0];
    }

    /**
     * Get building color with slight variation
     */
    _getBuildingColor(baseColor, random) {
        // Add slight color variation
        const variations = [
            baseColor,
            this._adjustColorBrightness(baseColor, random.nextFloat(-0.1, 0.1)),
            this._adjustColorBrightness(baseColor, random.nextFloat(-0.05, 0.05))
        ];
        
        return random.choice(variations);
    }

    /**
     * Generate additional building features
     */
    _generateBuildingFeatures(buildingType, districtType, random) {
        const features = {
            hasRoof: true,
            roofType: random.choice(['flat', 'pitched', 'complex']),
            hasWindows: true,
            windowPattern: random.choice(['regular', 'irregular', 'modern']),
            hasEntrance: true,
            entranceType: random.choice(['front', 'side', 'corner'])
        };
        
        // Add type-specific features
        switch (buildingType) {
            case 'HOUSE':
                features.hasGarden = random.nextBoolean(0.6);
                features.hasDriveway = random.nextBoolean(0.4);
                break;
            case 'APARTMENT':
                features.hasBalconies = random.nextBoolean(0.7);
                features.hasCourtyard = random.nextBoolean(0.3);
                break;
            case 'OFFICE':
                features.hasLogo = random.nextBoolean(0.5);
                features.hasParking = random.nextBoolean(0.8);
                break;
            case 'SHOP':
                features.hasSignage = random.nextBoolean(0.9);
                features.hasDisplayWindows = random.nextBoolean(0.8);
                break;
            case 'WAREHOUSE':
                features.hasLoadingDock = random.nextBoolean(0.7);
                features.hasFencing = random.nextBoolean(0.5);
                break;
            case 'FACTORY':
                features.hasChimney = random.nextBoolean(0.6);
                features.hasSilos = random.nextBoolean(0.3);
                break;
        }
        
        return features;
    }

    /**
     * Adjust color brightness
     */
    _adjustColorBrightness(color, factor) {
        // Simple brightness adjustment for hex colors
        if (color.startsWith('#')) {
            const hex = color.slice(1);
            const r = parseInt(hex.substr(0, 2), 16);
            const g = parseInt(hex.substr(2, 2), 16);
            const b = parseInt(hex.substr(4, 2), 16);
            
            const newR = Math.max(0, Math.min(255, Math.floor(r * (1 + factor))));
            const newG = Math.max(0, Math.min(255, Math.floor(g * (1 + factor))));
            const newB = Math.max(0, Math.min(255, Math.floor(b * (1 + factor))));
            
            return `#${newR.toString(16).padStart(2, '0')}${newG.toString(16).padStart(2, '0')}${newB.toString(16).padStart(2, '0')}`;
        }
        
        return color;
    }

    /**
     * Check if building placement conflicts with existing features
     */
    _checkBuildingConflicts(building, existingBuildings, naturalFeatures) {
        // Check against existing buildings
        for (const existing of existingBuildings) {
            if (this._rectanglesOverlap(building, existing)) {
                return true;
            }
        }
        
        // Check against natural features
        for (const feature of naturalFeatures) {
            if (feature.type === 'WATER' || feature.type === 'PARK') {
                if (this._rectangleOverlapsCircle(building, feature)) {
                    return true;
                }
            }
        }
        
        return false;
    }

    /**
     * Check if two rectangles overlap
     */
    _rectanglesOverlap(rect1, rect2) {
        return !(rect1.x + rect1.width < rect2.x || 
                rect2.x + rect2.width < rect1.x || 
                rect1.y + rect1.height < rect2.y || 
                rect2.y + rect2.height < rect1.y);
    }

    /**
     * Check if rectangle overlaps with circle
     */
    _rectangleOverlapsCircle(rect, circle) {
        const distX = Math.abs(circle.x - rect.x - rect.width / 2);
        const distY = Math.abs(circle.y - rect.y - rect.height / 2);
        
        if (distX > (rect.width / 2 + circle.radius)) return false;
        if (distY > (rect.height / 2 + circle.radius)) return false;
        
        if (distX <= (rect.width / 2)) return true;
        if (distY <= (rect.height / 2)) return true;
        
        const dx = distX - rect.width / 2;
        const dy = distY - rect.height / 2;
        return (dx * dx + dy * dy <= (circle.radius * circle.radius));
    }
}
