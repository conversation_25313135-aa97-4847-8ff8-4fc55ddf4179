/**
 * Road Network Generator - Creates different types of road patterns
 */

export class RoadNetworkGenerator {
    constructor() {
        this.roadTypes = {
            HIGHWAY: { width: 8, color: '#444444', priority: 1 },
            MAJOR: { width: 6, color: '#666666', priority: 2 },
            MINOR: { width: 4, color: '#888888', priority: 3 },
            RESIDENTIAL: { width: 2, color: '#aaaaaa', priority: 4 }
        };
    }

    /**
     * Generate road network based on configuration
     */
    async generate(config, random) {
        const { pattern, density, bounds } = config;
        
        let roads = [];
        
        switch (pattern) {
            case 'grid':
                roads = this._generateGridPattern(bounds, density, random);
                break;
            case 'organic':
                roads = this._generateOrganicPattern(bounds, density, random);
                break;
            case 'hybrid':
                roads = this._generateHybridPattern(bounds, density, random);
                break;
            default:
                roads = this._generateGridPattern(bounds, density, random);
        }
        
        // Add intersections
        roads = this._addIntersections(roads);
        
        return roads;
    }

    /**
     * Generate grid-based road pattern
     */
    _generateGridPattern(bounds, density, random) {
        const roads = [];
        const spacing = 80 / density; // Base spacing adjusted by density
        
        // Add some randomness to grid spacing
        const spacingVariation = spacing * 0.3;
        
        // Vertical roads
        for (let x = spacing; x < bounds.width - spacing; x += spacing + random.nextFloat(-spacingVariation, spacingVariation)) {
            const road = {
                id: `v_${roads.length}`,
                type: this._selectRoadType(random),
                points: [
                    { x: x, y: 0 },
                    { x: x, y: bounds.height }
                ],
                direction: 'vertical'
            };
            roads.push(road);
        }
        
        // Horizontal roads
        for (let y = spacing; y < bounds.height - spacing; y += spacing + random.nextFloat(-spacingVariation, spacingVariation)) {
            const road = {
                id: `h_${roads.length}`,
                type: this._selectRoadType(random),
                points: [
                    { x: 0, y: y },
                    { x: bounds.width, y: y }
                ],
                direction: 'horizontal'
            };
            roads.push(road);
        }
        
        return roads;
    }

    /**
     * Generate organic/curved road pattern
     */
    _generateOrganicPattern(bounds, density, random) {
        const roads = [];
        const numMainRoads = Math.floor(density * 8) + 3;
        
        // Generate main arterial roads
        for (let i = 0; i < numMainRoads; i++) {
            const road = this._generateOrganicRoad(bounds, random, 'MAJOR');
            roads.push(road);
        }
        
        // Generate secondary roads
        const numSecondaryRoads = Math.floor(density * 15) + 5;
        for (let i = 0; i < numSecondaryRoads; i++) {
            const road = this._generateOrganicRoad(bounds, random, 'MINOR');
            roads.push(road);
        }
        
        // Generate residential roads
        const numResidentialRoads = Math.floor(density * 25) + 10;
        for (let i = 0; i < numResidentialRoads; i++) {
            const road = this._generateOrganicRoad(bounds, random, 'RESIDENTIAL');
            roads.push(road);
        }
        
        return roads;
    }

    /**
     * Generate hybrid pattern (grid + organic)
     */
    _generateHybridPattern(bounds, density, random) {
        const roads = [];
        
        // Start with a loose grid
        const gridRoads = this._generateGridPattern(bounds, density * 0.6, random);
        roads.push(...gridRoads);
        
        // Add organic roads to fill gaps
        const numOrganicRoads = Math.floor(density * 10) + 5;
        for (let i = 0; i < numOrganicRoads; i++) {
            const road = this._generateOrganicRoad(bounds, random, 'MINOR');
            roads.push(road);
        }
        
        return roads;
    }

    /**
     * Generate a single organic road with curves
     */
    _generateOrganicRoad(bounds, random, roadType) {
        const points = [];
        const numPoints = random.nextInt(3, 8);
        
        // Start point
        let currentPoint = {
            x: random.nextFloat(bounds.width * 0.1, bounds.width * 0.9),
            y: random.nextFloat(bounds.height * 0.1, bounds.height * 0.9)
        };
        points.push(currentPoint);
        
        // Generate path with some direction bias
        let direction = random.nextAngle();
        const directionChange = Math.PI / 4; // Max direction change per segment
        
        for (let i = 1; i < numPoints; i++) {
            // Vary direction slightly
            direction += random.nextFloat(-directionChange, directionChange);
            
            // Calculate next point
            const distance = random.nextFloat(50, 150);
            const nextPoint = {
                x: currentPoint.x + Math.cos(direction) * distance,
                y: currentPoint.y + Math.sin(direction) * distance
            };
            
            // Keep within bounds
            nextPoint.x = Math.max(0, Math.min(bounds.width, nextPoint.x));
            nextPoint.y = Math.max(0, Math.min(bounds.height, nextPoint.y));
            
            points.push(nextPoint);
            currentPoint = nextPoint;
        }
        
        return {
            id: `organic_${Date.now()}_${random.nextInt(1000, 9999)}`,
            type: roadType,
            points: points,
            direction: 'organic'
        };
    }

    /**
     * Select road type based on probability
     */
    _selectRoadType(random) {
        const roll = random.next();
        
        if (roll < 0.05) return 'HIGHWAY';
        if (roll < 0.2) return 'MAJOR';
        if (roll < 0.5) return 'MINOR';
        return 'RESIDENTIAL';
    }

    /**
     * Add intersection data to roads
     */
    _addIntersections(roads) {
        const intersections = [];
        
        // Find intersections between roads
        for (let i = 0; i < roads.length; i++) {
            for (let j = i + 1; j < roads.length; j++) {
                const intersection = this._findIntersection(roads[i], roads[j]);
                if (intersection) {
                    intersections.push({
                        id: `intersection_${i}_${j}`,
                        point: intersection,
                        roads: [roads[i].id, roads[j].id]
                    });
                }
            }
        }
        
        // Add intersection data to roads
        roads.forEach(road => {
            road.intersections = intersections.filter(int => 
                int.roads.includes(road.id)
            );
        });
        
        return roads;
    }

    /**
     * Find intersection point between two roads (simplified)
     */
    _findIntersection(road1, road2) {
        // For simplicity, only handle straight line intersections
        if (road1.points.length === 2 && road2.points.length === 2) {
            return this._lineIntersection(
                road1.points[0], road1.points[1],
                road2.points[0], road2.points[1]
            );
        }
        return null;
    }

    /**
     * Calculate intersection point of two line segments
     */
    _lineIntersection(p1, p2, p3, p4) {
        const denom = (p1.x - p2.x) * (p3.y - p4.y) - (p1.y - p2.y) * (p3.x - p4.x);
        
        if (Math.abs(denom) < 0.001) {
            return null; // Lines are parallel
        }
        
        const t = ((p1.x - p3.x) * (p3.y - p4.y) - (p1.y - p3.y) * (p3.x - p4.x)) / denom;
        const u = -((p1.x - p2.x) * (p1.y - p3.y) - (p1.y - p2.y) * (p1.x - p3.x)) / denom;
        
        if (t >= 0 && t <= 1 && u >= 0 && u <= 1) {
            return {
                x: p1.x + t * (p2.x - p1.x),
                y: p1.y + t * (p2.y - p1.y)
            };
        }
        
        return null; // No intersection within line segments
    }
}
