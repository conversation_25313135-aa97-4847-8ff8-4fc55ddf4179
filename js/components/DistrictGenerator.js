/**
 * District Generator - Creates zoned areas for different city functions
 */

export class DistrictGenerator {
    constructor() {
        this.districtTypes = {
            RESIDENTIAL: {
                color: '#90EE90',
                density: 0.7,
                buildingHeight: { min: 1, max: 4 },
                description: 'Housing and residential areas'
            },
            COMMERCIAL: {
                color: '#FFB6C1',
                density: 0.8,
                buildingHeight: { min: 2, max: 8 },
                description: 'Shopping, offices, and services'
            },
            INDUSTRIAL: {
                color: '#DDA0DD',
                density: 0.6,
                buildingHeight: { min: 1, max: 3 },
                description: 'Manufacturing and warehouses'
            },
            MIXED: {
                color: '#F0E68C',
                density: 0.75,
                buildingHeight: { min: 1, max: 6 },
                description: 'Mixed-use development'
            }
        };
    }

    /**
     * Generate districts based on road network and configuration
     */
    async generate(config, random) {
        const { types, roads, bounds } = config;
        const districts = [];

        // Validate inputs
        if (!bounds || !bounds.width || !bounds.height) {
            console.error('Invalid bounds provided to DistrictGenerator');
            return districts;
        }

        // Create district seeds based on road intersections and random points
        const seeds = this._generateDistrictSeeds(roads, bounds, random);

        if (seeds.length === 0) {
            console.warn('No seeds generated for districts');
            return districts;
        }

        // Generate districts using Voronoi-like approach
        const voronoiCells = this._generateVoronoiCells(seeds, bounds, random);

        // Assign district types to cells
        for (let i = 0; i < voronoiCells.length; i++) {
            const cell = voronoiCells[i];
            const districtType = this._selectDistrictType(types, cell, random);

            if (districtType && cell.polygon && cell.polygon.length > 2) {
                const district = {
                    id: `district_${i}`,
                    type: districtType,
                    polygon: cell.polygon,
                    center: cell.center,
                    area: this._calculatePolygonArea(cell.polygon),
                    properties: this.districtTypes[districtType],
                    blocks: []
                };

                // Subdivide district into blocks
                district.blocks = this._subdivideIntoBlocks(district, roads, random);

                districts.push(district);
            }
        }

        return districts;
    }

    /**
     * Generate seed points for district creation
     */
    _generateDistrictSeeds(roads, bounds, random) {
        const seeds = [];

        // Add seeds at major road intersections
        if (roads && roads.length > 0) {
            roads.forEach(road => {
                if (road.intersections && road.intersections.length > 0) {
                    road.intersections.forEach(intersection => {
                        if (intersection.point) {
                            seeds.push({
                                x: intersection.point.x,
                                y: intersection.point.y,
                                weight: 2 // Higher weight for intersection-based seeds
                            });
                        }
                    });
                }
            });
        }

        // Always add random seeds to ensure we have districts
        const numRandomSeeds = Math.max(6, random.nextInt(8, 15));
        for (let i = 0; i < numRandomSeeds; i++) {
            seeds.push({
                x: random.nextFloat(bounds.width * 0.1, bounds.width * 0.9),
                y: random.nextFloat(bounds.height * 0.1, bounds.height * 0.9),
                weight: 1
            });
        }

        return seeds;
    }

    /**
     * Generate Voronoi-like cells for districts
     */
    _generateVoronoiCells(seeds, bounds, random) {
        const cells = [];
        const gridSize = 20; // Resolution for Voronoi calculation
        
        // Create a simplified Voronoi diagram
        for (let i = 0; i < seeds.length; i++) {
            const seed = seeds[i];
            const polygon = this._generateCellPolygon(seed, seeds, bounds, gridSize);
            
            if (polygon.length > 2) {
                cells.push({
                    center: seed,
                    polygon: polygon,
                    seedIndex: i
                });
            }
        }
        
        return cells;
    }

    /**
     * Generate polygon for a Voronoi cell (simplified)
     */
    _generateCellPolygon(seed, allSeeds, bounds, gridSize) {
        // Validate inputs
        if (!seed || typeof seed.x !== 'number' || typeof seed.y !== 'number') {
            console.warn('Invalid seed provided to _generateCellPolygon');
            return [];
        }

        if (!bounds || !bounds.width || !bounds.height) {
            console.warn('Invalid bounds provided to _generateCellPolygon');
            return [];
        }

        const polygon = [];
        const radius = Math.min(100, bounds.width / 8, bounds.height / 8); // Adaptive radius
        const numPoints = 8; // Number of polygon vertices

        // Generate octagonal approximation
        for (let i = 0; i < numPoints; i++) {
            const angle = (i / numPoints) * Math.PI * 2;
            let x = seed.x + Math.cos(angle) * radius;
            let y = seed.y + Math.sin(angle) * radius;

            // Adjust based on nearby seeds (simplified Voronoi)
            if (allSeeds && allSeeds.length > 0) {
                allSeeds.forEach(otherSeed => {
                    if (otherSeed !== seed && otherSeed.x !== undefined && otherSeed.y !== undefined) {
                        const dist = this._distance(seed, otherSeed);
                        if (dist < radius * 2 && dist > 0) {
                            const influence = 1 - (dist / (radius * 2));
                            const dx = seed.x - otherSeed.x;
                            const dy = seed.y - otherSeed.y;
                            x += dx * influence * 0.3;
                            y += dy * influence * 0.3;
                        }
                    }
                });
            }

            // Keep within bounds
            x = Math.max(10, Math.min(bounds.width - 10, x));
            y = Math.max(10, Math.min(bounds.height - 10, y));

            polygon.push({ x, y });
        }

        return polygon;
    }

    /**
     * Select district type based on configuration and location
     */
    _selectDistrictType(enabledTypes, cell, random) {
        const availableTypes = Object.keys(enabledTypes).filter(type => enabledTypes[type]);
        
        if (availableTypes.length === 0) {
            return null;
        }
        
        // Add some logic for realistic district placement
        const centerDistance = this._distance(cell.center, { x: 600, y: 400 }); // Assume center of map
        const normalizedDistance = centerDistance / 400; // Normalize to 0-1
        
        let weights = {};
        
        if (availableTypes.includes('COMMERCIAL')) {
            // Commercial districts prefer city center
            weights.COMMERCIAL = Math.max(0.1, 1 - normalizedDistance);
        }
        
        if (availableTypes.includes('RESIDENTIAL')) {
            // Residential districts prefer medium distance from center
            weights.RESIDENTIAL = normalizedDistance < 0.5 ? 0.8 : 1.2;
        }
        
        if (availableTypes.includes('INDUSTRIAL')) {
            // Industrial districts prefer outskirts
            weights.INDUSTRIAL = normalizedDistance > 0.3 ? 1.0 : 0.3;
        }
        
        // Select based on weighted random
        return this._weightedRandomChoice(weights, random);
    }

    /**
     * Subdivide district into city blocks
     */
    _subdivideIntoBlocks(district, roads, random) {
        const blocks = [];
        const blockSize = 60 + random.nextFloat(-20, 20);
        
        // Simple grid-based block subdivision
        const bounds = this._getPolygonBounds(district.polygon);
        
        for (let x = bounds.minX; x < bounds.maxX; x += blockSize) {
            for (let y = bounds.minY; y < bounds.maxY; y += blockSize) {
                const blockCenter = { x: x + blockSize / 2, y: y + blockSize / 2 };
                
                // Check if block center is inside district
                if (this._pointInPolygon(blockCenter, district.polygon)) {
                    const block = {
                        id: `block_${district.id}_${blocks.length}`,
                        center: blockCenter,
                        size: blockSize,
                        districtType: district.type,
                        buildingLots: []
                    };
                    
                    // Generate building lots within block
                    block.buildingLots = this._generateBuildingLots(block, random);
                    
                    blocks.push(block);
                }
            }
        }
        
        return blocks;
    }

    /**
     * Generate building lots within a block
     */
    _generateBuildingLots(block, random) {
        const lots = [];
        const lotSize = 15 + random.nextFloat(-5, 5);
        const lotsPerSide = Math.floor(block.size / lotSize);
        
        for (let i = 0; i < lotsPerSide; i++) {
            for (let j = 0; j < lotsPerSide; j++) {
                const lot = {
                    x: block.center.x - block.size / 2 + i * lotSize,
                    y: block.center.y - block.size / 2 + j * lotSize,
                    width: lotSize,
                    height: lotSize,
                    occupied: false
                };
                lots.push(lot);
            }
        }
        
        return lots;
    }

    /**
     * Utility functions
     */
    _distance(p1, p2) {
        return Math.sqrt(Math.pow(p2.x - p1.x, 2) + Math.pow(p2.y - p1.y, 2));
    }

    _calculatePolygonArea(polygon) {
        let area = 0;
        for (let i = 0; i < polygon.length; i++) {
            const j = (i + 1) % polygon.length;
            area += polygon[i].x * polygon[j].y;
            area -= polygon[j].x * polygon[i].y;
        }
        return Math.abs(area) / 2;
    }

    _getPolygonBounds(polygon) {
        let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity;
        
        polygon.forEach(point => {
            minX = Math.min(minX, point.x);
            minY = Math.min(minY, point.y);
            maxX = Math.max(maxX, point.x);
            maxY = Math.max(maxY, point.y);
        });
        
        return { minX, minY, maxX, maxY };
    }

    _pointInPolygon(point, polygon) {
        let inside = false;
        for (let i = 0, j = polygon.length - 1; i < polygon.length; j = i++) {
            if (((polygon[i].y > point.y) !== (polygon[j].y > point.y)) &&
                (point.x < (polygon[j].x - polygon[i].x) * (point.y - polygon[i].y) / (polygon[j].y - polygon[i].y) + polygon[i].x)) {
                inside = !inside;
            }
        }
        return inside;
    }

    _weightedRandomChoice(weights, random) {
        const totalWeight = Object.values(weights).reduce((sum, weight) => sum + weight, 0);
        let randomValue = random.nextFloat(0, totalWeight);
        
        for (const [choice, weight] of Object.entries(weights)) {
            randomValue -= weight;
            if (randomValue <= 0) {
                return choice;
            }
        }
        
        return Object.keys(weights)[0]; // Fallback
    }
}
