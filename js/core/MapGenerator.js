/**
 * Core MapGenerator class that orchestrates all map generation components
 */

import { SeededRandom } from '../utils/SeededRandom.js';
import { RoadNetworkGenerator } from '../components/RoadNetworkGenerator.js';
import { DistrictGenerator } from '../components/DistrictGenerator.js';
import { BuildingGenerator } from '../components/BuildingGenerator.js';
import { NaturalFeatureGenerator } from '../components/NaturalFeatureGenerator.js';
import { InfrastructureGenerator } from '../components/InfrastructureGenerator.js';
import { MapRenderer } from '../rendering/MapRenderer.js';

export class MapGenerator {
    constructor(canvas) {
        this.canvas = canvas;
        this.ctx = canvas.getContext('2d');
        this.renderer = new MapRenderer(this.ctx);
        
        // Initialize components
        this.roadGenerator = new RoadNetworkGenerator();
        this.districtGenerator = new DistrictGenerator();
        this.buildingGenerator = new BuildingGenerator();
        this.naturalFeatureGenerator = new NaturalFeatureGenerator();
        this.infrastructureGenerator = new InfrastructureGenerator();
        
        // Map data
        this.mapData = {
            roads: [],
            districts: [],
            buildings: [],
            naturalFeatures: [],
            infrastructure: [],
            bounds: { width: 0, height: 0 }
        };
        
        // Default configuration
        this.config = {
            seed: null,
            mapSize: { width: 1200, height: 800 },
            roadPattern: 'hybrid',
            roadDensity: 0.6,
            buildingDensity: 0.7,
            districts: {
                residential: true,
                commercial: true,
                industrial: true
            },
            features: {
                parks: true,
                water: true,
                infrastructure: true
            }
        };
    }

    /**
     * Update configuration and regenerate if needed
     */
    updateConfig(newConfig) {
        this.config = { ...this.config, ...newConfig };
        
        // Update canvas size if changed
        if (newConfig.mapSize) {
            this.canvas.width = newConfig.mapSize.width;
            this.canvas.height = newConfig.mapSize.height;
            this.mapData.bounds = newConfig.mapSize;
        }
    }

    /**
     * Generate a complete city map
     */
    async generateMap() {
        try {
            // Initialize random number generator
            const seed = this.config.seed || this._generateSeed();
            const random = new SeededRandom(seed);
            
            // Clear previous data
            this._clearMapData();
            
            // Set map bounds
            this.mapData.bounds = this.config.mapSize;
            
            // Generation phases
            console.log('Generating road network...');
            await this._generateRoads(random);
            
            console.log('Creating districts...');
            await this._generateDistricts(random);
            
            console.log('Placing natural features...');
            await this._generateNaturalFeatures(random);
            
            console.log('Generating buildings...');
            await this._generateBuildings(random);
            
            console.log('Adding infrastructure...');
            await this._generateInfrastructure(random);
            
            console.log('Rendering map...');
            await this._renderMap();
            
            return {
                success: true,
                seed: seed,
                stats: this._getMapStats()
            };
            
        } catch (error) {
            console.error('Map generation failed:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Generate road network
     */
    async _generateRoads(random) {
        const roadConfig = {
            pattern: this.config.roadPattern,
            density: this.config.roadDensity,
            bounds: this.mapData.bounds
        };
        
        this.mapData.roads = await this.roadGenerator.generate(roadConfig, random);
    }

    /**
     * Generate districts/zones
     */
    async _generateDistricts(random) {
        const districtConfig = {
            types: this.config.districts,
            roads: this.mapData.roads,
            bounds: this.mapData.bounds
        };
        
        this.mapData.districts = await this.districtGenerator.generate(districtConfig, random);
    }

    /**
     * Generate natural features
     */
    async _generateNaturalFeatures(random) {
        if (!this.config.features.parks && !this.config.features.water) {
            return;
        }
        
        const featureConfig = {
            parks: this.config.features.parks,
            water: this.config.features.water,
            roads: this.mapData.roads,
            districts: this.mapData.districts,
            bounds: this.mapData.bounds
        };
        
        this.mapData.naturalFeatures = await this.naturalFeatureGenerator.generate(featureConfig, random);
    }

    /**
     * Generate buildings
     */
    async _generateBuildings(random) {
        const buildingConfig = {
            density: this.config.buildingDensity,
            roads: this.mapData.roads,
            districts: this.mapData.districts,
            naturalFeatures: this.mapData.naturalFeatures,
            bounds: this.mapData.bounds
        };
        
        this.mapData.buildings = await this.buildingGenerator.generate(buildingConfig, random);
    }

    /**
     * Generate infrastructure
     */
    async _generateInfrastructure(random) {
        if (!this.config.features.infrastructure) {
            return;
        }
        
        const infraConfig = {
            roads: this.mapData.roads,
            districts: this.mapData.districts,
            buildings: this.mapData.buildings,
            bounds: this.mapData.bounds
        };
        
        this.mapData.infrastructure = await this.infrastructureGenerator.generate(infraConfig, random);
    }

    /**
     * Render the complete map
     */
    async _renderMap() {
        // Clear canvas
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
        
        // Render in layers (back to front)
        await this.renderer.renderBackground(this.mapData.bounds);
        await this.renderer.renderDistricts(this.mapData.districts);
        await this.renderer.renderNaturalFeatures(this.mapData.naturalFeatures);
        await this.renderer.renderRoads(this.mapData.roads);
        await this.renderer.renderBuildings(this.mapData.buildings);
        await this.renderer.renderInfrastructure(this.mapData.infrastructure);
    }

    /**
     * Export map as image
     */
    exportImage() {
        const link = document.createElement('a');
        link.download = `city-map-${Date.now()}.png`;
        link.href = this.canvas.toDataURL();
        link.click();
    }

    /**
     * Export map data as JSON
     */
    exportData() {
        const data = {
            config: this.config,
            mapData: this.mapData,
            timestamp: new Date().toISOString()
        };
        
        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
        const link = document.createElement('a');
        link.download = `city-map-data-${Date.now()}.json`;
        link.href = URL.createObjectURL(blob);
        link.click();
    }

    /**
     * Clear all map data
     */
    _clearMapData() {
        this.mapData = {
            roads: [],
            districts: [],
            buildings: [],
            naturalFeatures: [],
            infrastructure: [],
            bounds: this.config.mapSize
        };
    }

    /**
     * Generate a random seed
     */
    _generateSeed() {
        return Math.random().toString(36).substring(2, 15);
    }

    /**
     * Get map statistics
     */
    _getMapStats() {
        return {
            roads: this.mapData.roads.length,
            districts: this.mapData.districts.length,
            buildings: this.mapData.buildings.length,
            naturalFeatures: this.mapData.naturalFeatures.length,
            infrastructure: this.mapData.infrastructure.length
        };
    }
}
