/**
 * Map Renderer - Handles all canvas rendering for the city map
 */

export class MapRenderer {
    constructor(ctx) {
        this.ctx = ctx;
        this.scale = 1;
        this.offsetX = 0;
        this.offsetY = 0;
    }

    /**
     * Render background
     */
    async renderBackground(bounds) {
        const ctx = this.ctx;
        
        // Clear canvas
        ctx.fillStyle = '#f0f8ff'; // Light blue background
        ctx.fillRect(0, 0, bounds.width, bounds.height);
        
        // Add subtle grid pattern
        ctx.strokeStyle = '#e6f3ff';
        ctx.lineWidth = 0.5;
        ctx.setLineDash([2, 2]);
        
        const gridSize = 50;
        for (let x = 0; x <= bounds.width; x += gridSize) {
            ctx.beginPath();
            ctx.moveTo(x, 0);
            ctx.lineTo(x, bounds.height);
            ctx.stroke();
        }
        
        for (let y = 0; y <= bounds.height; y += gridSize) {
            ctx.beginPath();
            ctx.moveTo(0, y);
            ctx.lineTo(bounds.width, y);
            ctx.stroke();
        }
        
        ctx.setLineDash([]);
    }

    /**
     * Render districts
     */
    async renderDistricts(districts) {
        const ctx = this.ctx;

        for (const district of districts) {
            // Validate district data
            if (!district || !district.polygon || !district.properties || !district.center) {
                console.warn('Invalid district data, skipping render');
                continue;
            }

            // Fill district area
            ctx.fillStyle = district.properties.color + '40'; // Semi-transparent
            ctx.strokeStyle = district.properties.color;
            ctx.lineWidth = 1;

            this._drawPolygon(district.polygon, true, true);

            // Add district label
            ctx.fillStyle = '#333';
            ctx.font = '12px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(
                district.type,
                district.center.x,
                district.center.y
            );
        }
    }

    /**
     * Render natural features
     */
    async renderNaturalFeatures(features) {
        const ctx = this.ctx;
        
        for (const feature of features) {
            ctx.fillStyle = feature.color;
            ctx.strokeStyle = this._darkenColor(feature.color, 0.2);
            ctx.lineWidth = 2;
            
            this._renderFeatureGeometry(feature.geometry);
            
            // Add feature elements (trees, paths, etc.)
            this._renderFeatureElements(feature);
        }
    }

    /**
     * Render roads
     */
    async renderRoads(roads) {
        const ctx = this.ctx;
        
        // Sort roads by priority (highways first)
        const sortedRoads = roads.sort((a, b) => {
            const priorities = { HIGHWAY: 1, MAJOR: 2, MINOR: 3, RESIDENTIAL: 4 };
            return priorities[a.type] - priorities[b.type];
        });
        
        for (const road of sortedRoads) {
            const roadConfig = this._getRoadConfig(road.type);
            
            ctx.strokeStyle = roadConfig.color;
            ctx.lineWidth = roadConfig.width;
            ctx.lineCap = 'round';
            ctx.lineJoin = 'round';
            
            // Draw road path
            if (road.points.length >= 2) {
                ctx.beginPath();
                ctx.moveTo(road.points[0].x, road.points[0].y);
                
                for (let i = 1; i < road.points.length; i++) {
                    ctx.lineTo(road.points[i].x, road.points[i].y);
                }
                
                ctx.stroke();
            }
            
            // Draw intersections
            if (road.intersections) {
                for (const intersection of road.intersections) {
                    this._renderIntersection(intersection);
                }
            }
        }
    }

    /**
     * Render buildings
     */
    async renderBuildings(buildings) {
        const ctx = this.ctx;
        
        for (const building of buildings) {
            ctx.save();
            
            // Apply rotation if any
            if (building.rotation) {
                ctx.translate(building.x + building.width / 2, building.y + building.height / 2);
                ctx.rotate(building.rotation * Math.PI / 180);
                ctx.translate(-building.width / 2, -building.height / 2);
            } else {
                ctx.translate(building.x, building.y);
            }
            
            // Draw building base
            ctx.fillStyle = building.color;
            ctx.strokeStyle = this._darkenColor(building.color, 0.3);
            ctx.lineWidth = 1;
            
            ctx.fillRect(0, 0, building.width, building.height);
            ctx.strokeRect(0, 0, building.width, building.height);
            
            // Add building details
            this._renderBuildingDetails(building);
            
            ctx.restore();
        }
    }

    /**
     * Render infrastructure
     */
    async renderInfrastructure(infrastructure) {
        const ctx = this.ctx;
        
        for (const infra of infrastructure) {
            // Draw infrastructure building
            ctx.fillStyle = infra.color;
            ctx.strokeStyle = this._darkenColor(infra.color, 0.3);
            ctx.lineWidth = 2;
            
            ctx.fillRect(infra.x, infra.y, infra.width, infra.height);
            ctx.strokeRect(infra.x, infra.y, infra.width, infra.height);
            
            // Add icon or label
            ctx.fillStyle = '#fff';
            ctx.font = 'bold 16px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            
            const centerX = infra.x + infra.width / 2;
            const centerY = infra.y + infra.height / 2;
            
            if (infra.icon) {
                ctx.fillText(infra.icon, centerX, centerY);
            }
            
            // Add label below
            ctx.fillStyle = '#333';
            ctx.font = '10px Arial';
            ctx.fillText(
                infra.type.replace('_', ' '),
                centerX,
                infra.y + infra.height + 12
            );
        }
    }

    /**
     * Render feature geometry based on type
     */
    _renderFeatureGeometry(geometry) {
        const ctx = this.ctx;
        
        switch (geometry.type) {
            case 'circle':
                ctx.beginPath();
                ctx.arc(geometry.center.x, geometry.center.y, geometry.radius, 0, Math.PI * 2);
                ctx.fill();
                ctx.stroke();
                break;
                
            case 'ellipse':
                ctx.save();
                ctx.translate(geometry.center.x, geometry.center.y);
                ctx.rotate(geometry.rotation || 0);
                ctx.scale(geometry.radiusX / geometry.radiusY, 1);
                ctx.beginPath();
                ctx.arc(0, 0, geometry.radiusY, 0, Math.PI * 2);
                ctx.fill();
                ctx.stroke();
                ctx.restore();
                break;
                
            case 'rectangle':
                ctx.fillRect(geometry.x, geometry.y, geometry.width, geometry.height);
                ctx.strokeRect(geometry.x, geometry.y, geometry.width, geometry.height);
                break;
                
            case 'polygon':
                this._drawPolygon(geometry.points, true, true);
                break;
                
            case 'path':
                ctx.lineWidth = geometry.width || 4;
                ctx.beginPath();
                ctx.moveTo(geometry.points[0].x, geometry.points[0].y);
                for (let i = 1; i < geometry.points.length; i++) {
                    ctx.lineTo(geometry.points[i].x, geometry.points[i].y);
                }
                ctx.stroke();
                break;
        }
    }

    /**
     * Render feature elements (trees, paths, etc.)
     */
    _renderFeatureElements(feature) {
        const ctx = this.ctx;
        
        if (feature.features.includes('trees')) {
            this._renderTrees(feature);
        }
        
        if (feature.features.includes('paths')) {
            this._renderPaths(feature);
        }
        
        if (feature.features.includes('fountain')) {
            this._renderFountain(feature);
        }
    }

    /**
     * Render trees in a feature
     */
    _renderTrees(feature) {
        const ctx = this.ctx;
        const numTrees = Math.floor(feature.size / 20);
        
        ctx.fillStyle = '#228B22';
        
        for (let i = 0; i < numTrees; i++) {
            const angle = (i / numTrees) * Math.PI * 2;
            const radius = feature.size / 3;
            const x = feature.x + Math.cos(angle) * radius;
            const y = feature.y + Math.sin(angle) * radius;
            
            ctx.beginPath();
            ctx.arc(x, y, 3, 0, Math.PI * 2);
            ctx.fill();
        }
    }

    /**
     * Render paths in a feature
     */
    _renderPaths(feature) {
        const ctx = this.ctx;
        
        ctx.strokeStyle = '#DEB887';
        ctx.lineWidth = 2;
        
        // Simple cross path
        ctx.beginPath();
        ctx.moveTo(feature.x - feature.size / 2, feature.y);
        ctx.lineTo(feature.x + feature.size / 2, feature.y);
        ctx.moveTo(feature.x, feature.y - feature.size / 2);
        ctx.lineTo(feature.x, feature.y + feature.size / 2);
        ctx.stroke();
    }

    /**
     * Render fountain
     */
    _renderFountain(feature) {
        const ctx = this.ctx;
        
        ctx.fillStyle = '#4169E1';
        ctx.beginPath();
        ctx.arc(feature.x, feature.y, 8, 0, Math.PI * 2);
        ctx.fill();
        
        ctx.strokeStyle = '#fff';
        ctx.lineWidth = 2;
        ctx.stroke();
    }

    /**
     * Render building details
     */
    _renderBuildingDetails(building) {
        const ctx = this.ctx;
        
        // Windows
        if (building.features.hasWindows) {
            this._renderWindows(building);
        }
        
        // Entrance
        if (building.features.hasEntrance) {
            this._renderEntrance(building);
        }
        
        // Roof details
        if (building.features.roofType === 'pitched') {
            this._renderPitchedRoof(building);
        }
    }

    /**
     * Render windows on building
     */
    _renderWindows(building) {
        const ctx = this.ctx;
        const windowSize = 2;
        const spacing = 6;
        
        ctx.fillStyle = '#87CEEB';
        
        for (let x = spacing; x < building.width - spacing; x += spacing) {
            for (let y = spacing; y < building.height - spacing; y += spacing) {
                ctx.fillRect(x, y, windowSize, windowSize);
            }
        }
    }

    /**
     * Render building entrance
     */
    _renderEntrance(building) {
        const ctx = this.ctx;
        
        ctx.fillStyle = '#8B4513';
        const doorWidth = 4;
        const doorHeight = 6;
        const x = (building.width - doorWidth) / 2;
        const y = building.height - doorHeight;
        
        ctx.fillRect(x, y, doorWidth, doorHeight);
    }

    /**
     * Render pitched roof
     */
    _renderPitchedRoof(building) {
        const ctx = this.ctx;
        
        ctx.fillStyle = '#CD853F';
        ctx.beginPath();
        ctx.moveTo(0, 0);
        ctx.lineTo(building.width / 2, -8);
        ctx.lineTo(building.width, 0);
        ctx.closePath();
        ctx.fill();
    }

    /**
     * Render road intersection
     */
    _renderIntersection(intersection) {
        const ctx = this.ctx;
        
        ctx.fillStyle = '#555';
        ctx.beginPath();
        ctx.arc(intersection.point.x, intersection.point.y, 4, 0, Math.PI * 2);
        ctx.fill();
    }

    /**
     * Draw polygon
     */
    _drawPolygon(points, fill = false, stroke = false) {
        const ctx = this.ctx;

        // Validate points array
        if (!points || !Array.isArray(points) || points.length < 3) {
            console.warn('Invalid points array for polygon drawing');
            return;
        }

        // Validate that all points have x and y coordinates
        const validPoints = points.filter(point =>
            point && typeof point.x === 'number' && typeof point.y === 'number' &&
            !isNaN(point.x) && !isNaN(point.y)
        );

        if (validPoints.length < 3) {
            console.warn('Not enough valid points for polygon drawing');
            return;
        }

        ctx.beginPath();
        ctx.moveTo(validPoints[0].x, validPoints[0].y);

        for (let i = 1; i < validPoints.length; i++) {
            ctx.lineTo(validPoints[i].x, validPoints[i].y);
        }

        ctx.closePath();

        if (fill) ctx.fill();
        if (stroke) ctx.stroke();
    }

    /**
     * Get road configuration
     */
    _getRoadConfig(type) {
        const configs = {
            HIGHWAY: { width: 8, color: '#444444' },
            MAJOR: { width: 6, color: '#666666' },
            MINOR: { width: 4, color: '#888888' },
            RESIDENTIAL: { width: 2, color: '#aaaaaa' }
        };
        
        return configs[type] || configs.RESIDENTIAL;
    }

    /**
     * Darken color by percentage
     */
    _darkenColor(color, percent) {
        if (color.startsWith('#')) {
            const hex = color.slice(1);
            const r = parseInt(hex.substr(0, 2), 16);
            const g = parseInt(hex.substr(2, 2), 16);
            const b = parseInt(hex.substr(4, 2), 16);
            
            const newR = Math.floor(r * (1 - percent));
            const newG = Math.floor(g * (1 - percent));
            const newB = Math.floor(b * (1 - percent));
            
            return `#${newR.toString(16).padStart(2, '0')}${newG.toString(16).padStart(2, '0')}${newB.toString(16).padStart(2, '0')}`;
        }
        
        return color;
    }
}
