/**
 * Seeded random number generator for reproducible map generation
 * Uses a simple Linear Congruential Generator (LCG) algorithm
 */

export class SeededRandom {
    constructor(seed) {
        this.seed = this._hashSeed(seed);
        this.current = this.seed;
    }

    /**
     * Generate next random number between 0 and 1
     */
    next() {
        // LCG formula: (a * x + c) % m
        // Using values from Numerical Recipes
        this.current = (this.current * 1664525 + 1013904223) % Math.pow(2, 32);
        return this.current / Math.pow(2, 32);
    }

    /**
     * Generate random integer between min and max (inclusive)
     */
    nextInt(min, max) {
        return Math.floor(this.next() * (max - min + 1)) + min;
    }

    /**
     * Generate random float between min and max
     */
    nextFloat(min, max) {
        return this.next() * (max - min) + min;
    }

    /**
     * Generate random boolean with given probability
     */
    nextBoolean(probability = 0.5) {
        return this.next() < probability;
    }

    /**
     * Choose random element from array
     */
    choice(array) {
        if (array.length === 0) return null;
        return array[this.nextInt(0, array.length - 1)];
    }

    /**
     * Shuffle array in place using Fisher-Yates algorithm
     */
    shuffle(array) {
        for (let i = array.length - 1; i > 0; i--) {
            const j = this.nextInt(0, i);
            [array[i], array[j]] = [array[j], array[i]];
        }
        return array;
    }

    /**
     * Generate random point within bounds
     */
    nextPoint(bounds) {
        return {
            x: this.nextFloat(0, bounds.width),
            y: this.nextFloat(0, bounds.height)
        };
    }

    /**
     * Generate random point within circle
     */
    nextPointInCircle(center, radius) {
        const angle = this.nextFloat(0, Math.PI * 2);
        const r = Math.sqrt(this.next()) * radius;
        return {
            x: center.x + Math.cos(angle) * r,
            y: center.y + Math.sin(angle) * r
        };
    }

    /**
     * Generate random angle in radians
     */
    nextAngle() {
        return this.nextFloat(0, Math.PI * 2);
    }

    /**
     * Generate random color
     */
    nextColor(alpha = 1) {
        const r = this.nextInt(0, 255);
        const g = this.nextInt(0, 255);
        const b = this.nextInt(0, 255);
        return `rgba(${r}, ${g}, ${b}, ${alpha})`;
    }

    /**
     * Generate random color from palette
     */
    nextColorFromPalette(palette) {
        return this.choice(palette);
    }

    /**
     * Generate Gaussian (normal) distributed random number
     * Using Box-Muller transform
     */
    nextGaussian(mean = 0, stdDev = 1) {
        if (this._hasSpare) {
            this._hasSpare = false;
            return this._spare * stdDev + mean;
        }

        this._hasSpare = true;
        const u = this.next();
        const v = this.next();
        const mag = stdDev * Math.sqrt(-2.0 * Math.log(u));
        this._spare = mag * Math.cos(2.0 * Math.PI * v);
        return mag * Math.sin(2.0 * Math.PI * v) + mean;
    }

    /**
     * Reset to initial seed
     */
    reset() {
        this.current = this.seed;
        this._hasSpare = false;
        this._spare = 0;
    }

    /**
     * Get current seed
     */
    getSeed() {
        return this.seed;
    }

    /**
     * Hash string seed to number
     */
    _hashSeed(seed) {
        if (typeof seed === 'number') {
            return Math.abs(seed) % Math.pow(2, 32);
        }
        
        if (typeof seed === 'string') {
            let hash = 0;
            for (let i = 0; i < seed.length; i++) {
                const char = seed.charCodeAt(i);
                hash = ((hash << 5) - hash) + char;
                hash = hash & hash; // Convert to 32-bit integer
            }
            return Math.abs(hash) % Math.pow(2, 32);
        }
        
        // Default seed if invalid input
        return 12345;
    }
}
