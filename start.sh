#!/bin/bash

# City Map Generator - 一键启动脚本 (Linux/macOS)
# One-click startup script for City Map Generator

echo "🏙️  城市地图生成器 - City Map Generator"
echo "======================================"

# 检查端口是否被占用
check_port() {
    local port=$1
    if command -v lsof >/dev/null 2>&1; then
        if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null; then
            return 0  # 端口被占用
        fi
    elif command -v netstat >/dev/null 2>&1; then
        if netstat -ln | grep ":$port " >/dev/null; then
            return 0  # 端口被占用
        fi
    fi
    return 1  # 端口未被占用
}

# 查找可用端口
find_available_port() {
    local start_port=8000
    local port=$start_port
    
    while [ $port -lt 9000 ]; do
        if ! check_port $port; then
            echo $port
            return
        fi
        port=$((port + 1))
    done
    
    echo $start_port  # 如果都被占用，返回默认端口
}

# 获取本机IP地址
get_local_ip() {
    if command -v ip >/dev/null 2>&1; then
        ip route get ******* | grep -oP 'src \K\S+' 2>/dev/null
    elif command -v ifconfig >/dev/null 2>&1; then
        ifconfig | grep -Eo 'inet (addr:)?([0-9]*\.){3}[0-9]*' | grep -Eo '([0-9]*\.){3}[0-9]*' | grep -v '127.0.0.1' | head -1
    else
        echo "localhost"
    fi
}

# 启动服务器函数
start_server() {
    local port=$1
    local server_cmd=""
    
    # 检查 Python 3
    if command -v python3 >/dev/null 2>&1; then
        echo "✅ 使用 Python 3 启动服务器..."
        server_cmd="python3 -m http.server $port"
    # 检查 Python 2
    elif command -v python >/dev/null 2>&1; then
        echo "✅ 使用 Python 2 启动服务器..."
        server_cmd="python -m SimpleHTTPServer $port"
    # 检查 Node.js
    elif command -v node >/dev/null 2>&1; then
        if command -v npx >/dev/null 2>&1; then
            echo "✅ 使用 Node.js 启动服务器..."
            server_cmd="npx http-server -p $port"
        else
            echo "❌ 找到 Node.js 但缺少 npx，请安装 http-server: npm install -g http-server"
            exit 1
        fi
    # 检查 PHP
    elif command -v php >/dev/null 2>&1; then
        echo "✅ 使用 PHP 启动服务器..."
        server_cmd="php -S localhost:$port"
    else
        echo "❌ 未找到可用的服务器环境！"
        echo "请安装以下任一环境："
        echo "  - Python 3: https://www.python.org/"
        echo "  - Python 2: https://www.python.org/"
        echo "  - Node.js: https://nodejs.org/"
        echo "  - PHP: https://www.php.net/"
        exit 1
    fi
    
    echo "🚀 启动服务器: $server_cmd"
    echo ""
    
    # 启动服务器
    eval $server_cmd &
    SERVER_PID=$!
    
    return 0
}

# 主程序
main() {
    # 检查是否在正确的目录
    if [ ! -f "index.html" ]; then
        echo "❌ 错误：请在项目根目录运行此脚本"
        echo "当前目录：$(pwd)"
        echo "请确保 index.html 文件存在"
        exit 1
    fi
    
    # 查找可用端口
    PORT=$(find_available_port)
    
    echo "📂 项目目录: $(pwd)"
    echo "🔍 使用端口: $PORT"
    
    # 启动服务器
    start_server $PORT
    
    # 等待服务器启动
    echo "⏳ 等待服务器启动..."
    sleep 2
    
    # 获取访问地址
    LOCAL_IP=$(get_local_ip)
    
    echo ""
    echo "🎉 服务器启动成功！"
    echo "======================================"
    echo "📱 本地访问地址:"
    echo "   http://localhost:$PORT"
    echo "   http://127.0.0.1:$PORT"
    echo ""
    echo "🌐 局域网访问地址:"
    echo "   http://$LOCAL_IP:$PORT"
    echo ""
    echo "💡 使用说明:"
    echo "   1. 在浏览器中打开上述任一地址"
    echo "   2. 点击 'Generate Map' 按钮生成地图"
    echo "   3. 调整参数体验不同的城市布局"
    echo "   4. 使用导出功能保存生成的地图"
    echo ""
    echo "⚠️  按 Ctrl+C 停止服务器"
    echo "======================================"
    
    # 尝试自动打开浏览器
    if command -v open >/dev/null 2>&1; then
        # macOS
        echo "🔗 正在打开浏览器..."
        open "http://localhost:$PORT"
    elif command -v xdg-open >/dev/null 2>&1; then
        # Linux
        echo "🔗 正在打开浏览器..."
        xdg-open "http://localhost:$PORT"
    fi
    
    # 等待用户中断
    trap 'echo ""; echo "🛑 正在停止服务器..."; kill $SERVER_PID 2>/dev/null; echo "✅ 服务器已停止"; exit 0' INT
    
    # 保持脚本运行
    wait $SERVER_PID
}

# 运行主程序
main "$@"
